"""
冲突检测和管理服务
"""
from typing import List, Dict, Optional, Set, Tuple
from sqlalchemy.orm import Session
from app import models, schemas, crud
import logging

logger = logging.getLogger(__name__)


class ConflictDetectionService:
    """冲突检测服务"""
    
    # 需要检测冲突的字段
    CONFLICT_FIELDS = [
        'fan_qie', 'sheng_mu', 'yun_bu', 'sheng_diao',
        'kai_he', 'deng_di', 'xiao_yun', 'qing_zhuo', 'she'
    ]
    
    # 字段显示名称映射
    FIELD_DISPLAY_NAMES = {
        'fan_qie': '反切',
        'sheng_mu': '声母',
        'yun_bu': '韵部',
        'sheng_diao': '声调',
        'kai_he': '开合',
        'deng_di': '等第',
        'she': '摄',
        'qing_zhuo': '清浊',
        'xiao_yun': '小韵'
    }
    
    @staticmethod
    def detect_field_conflicts(sources: Dict[str, List[models.YinyunGyOrigin]], field_name: str) -> bool:
        """
        检测字段冲突 - 完全按照merge_all_equal.py的逻辑
        规则：
        1. 忽略空值（None或空字符串）
        2. 只有当有值的字段之间不同时才记为冲突
        3. 如果只有一个源有值，不算冲突
        """
        non_empty_values = set()

        for source_records in sources.values():
            for record in source_records:
                value = getattr(record, field_name, None)
                # 只考虑非空值
                if value is not None and str(value).strip():
                    non_empty_values.add(str(value).strip())

        # 只有当有2个或以上不同的非空值时才算冲突
        return len(non_empty_values) > 1

    @staticmethod
    def get_field_conflict_details(sources: Dict[str, List[models.YinyunGyOrigin]], field_name: str) -> Dict[str, str]:
        """
        获取字段冲突详情，用于调试 - 按照merge_all_equal.py的逻辑
        返回每个源的字段值
        """
        field_values = {}

        for source, source_records in sources.items():
            for record in source_records:
                value = getattr(record, field_name, None)
                if value is not None and str(value).strip():
                    field_values[source] = str(value).strip()
                else:
                    field_values[source] = "(空值)"

        return field_values
    
    @staticmethod
    def detect_conflicts_for_guangyun(db: Session, guangyun_record: models.YunshuGuangyun) -> Tuple[int, List[Dict]]:
        """
        为广韵记录检测冲突 - 完全按照merge_all_equal.py的逻辑
        返回: (冲突数量, 冲突详情列表)
        """
        # 获取关联的原始数据
        # 首先尝试通过ref字段获取
        origin_records = crud.gy_origin_crud.get_by_ref(db, guangyun_record.id)

        # 如果通过ref找不到数据，尝试通过Unicode和反切值查找
        if len(origin_records) < 2:
            origin_records = ConflictDetectionService._get_related_origin_records(db, guangyun_record)

        if len(origin_records) < 2:
            return 0, []

        # 按来源分类 - 按照merge_all_equal.py的逻辑
        sources = {'xxt': [], 'qx': [], 'yd': []}
        for record in origin_records:
            if record.source in sources:
                sources[record.source].append(record)

        # 检测冲突（shi_yi字段不参与冲突比较）- 按照merge_all_equal.py的逻辑
        conflict_fields = [
            'fan_qie', 'sheng_mu', 'yun_bu', 'sheng_diao', 'kai_he',
            'deng_di', 'she', 'qing_zhuo', 'xiao_yun'
        ]

        conflicts = 0
        conflict_details = []

        for field_name in conflict_fields:
            if ConflictDetectionService.detect_field_conflicts(sources, field_name):
                conflicts += 1

                # 获取各源的值
                field_values = ConflictDetectionService.get_field_conflict_details(sources, field_name)

                # 获取合并后的值（从guangyun记录中）
                merged_value = getattr(guangyun_record, field_name, None)
                merged_value = str(merged_value) if merged_value is not None else None

                # 合并规则 - 按照merge_all_equal.py的逻辑
                merge_rule = "xxt优先" if field_name != 'shi_yi' and field_name != 'xiao_yun' else "qx优先"

                conflict_info = {
                    'field_name': field_name,
                    'field_display_name': ConflictDetectionService.FIELD_DISPLAY_NAMES.get(field_name, field_name),
                    'field_values': field_values,
                    'merged_value': merged_value,
                    'merge_rule': merge_rule,
                    'sources': sources  # 保存sources用于创建冲突记录
                }

                conflict_details.append(conflict_info)

        return conflicts, conflict_details
    
    @staticmethod
    def _get_merge_rule(field_name: str) -> str:
        """获取字段的合并规则"""
        if field_name in ['shi_yi', 'xiao_yun']:
            return "qx优先"
        else:
            return "xxt优先"

    @staticmethod
    def _normalize_fanqie(fanqie: str) -> str:
        """标准化反切：如果不以"切"结尾，则补上"切"字"""
        if not fanqie:
            return fanqie

        # 如果不以"切"结尾，则补上"切"字
        if not fanqie.endswith('切'):
            return fanqie + '切'

        return fanqie

    @staticmethod
    def _get_related_origin_records(db: Session, guangyun_record: models.YunshuGuangyun) -> List[models.YinyunGyOrigin]:
        """
        获取与广韵记录相关的原始数据
        通过Unicode和反切值匹配
        """
        # 获取该Unicode的所有原始数据
        all_origin_records = crud.gy_origin_crud.get_by_unicode(db, guangyun_record.unicode)

        if not all_origin_records:
            return []

        # 标准化广韵记录的反切值
        guangyun_fanqie = ConflictDetectionService._normalize_fanqie(guangyun_record.fan_qie) if guangyun_record.fan_qie else None

        if not guangyun_fanqie:
            return []

        # 找到反切值匹配的原始数据
        related_records = []
        for record in all_origin_records:
            record_fanqie = ConflictDetectionService._normalize_fanqie(record.fan_qie) if record.fan_qie else None
            if record_fanqie and record_fanqie == guangyun_fanqie:
                related_records.append(record)

        return related_records
    
    @staticmethod
    def create_conflict_records(db: Session, guangyun_record: models.YunshuGuangyun,
                               conflict_details: List[Dict]) -> List[models.YunshuConflictRecord]:
        """创建冲突记录 - 按照merge_all_equal.py的逻辑"""
        created_records = []

        for conflict_info in conflict_details:
            # 获取各源的值 - 按照merge_all_equal.py的逻辑
            sources = conflict_info['sources']

            # 获取各源的值
            xxt_value = None
            qx_value = None
            yd_value = None

            for source, source_records in sources.items():
                for record in source_records:
                    value = getattr(record, conflict_info['field_name'], None)
                    if value is not None and str(value).strip():
                        if source == 'xxt':
                            xxt_value = str(value).strip()
                        elif source == 'qx':
                            qx_value = str(value).strip()
                        elif source == 'yd':
                            yd_value = str(value).strip()

            conflict_data = schemas.YunshuConflictRecordCreate(
                unicode=guangyun_record.unicode,
                hanzi=guangyun_record.hanzi,
                guangyun_id=guangyun_record.id,
                fan_qie=guangyun_record.fan_qie,
                field_name=conflict_info['field_name'],
                field_display_name=conflict_info['field_display_name'],
                xxt_value=xxt_value,
                qx_value=qx_value,
                yd_value=yd_value,
                merged_value=conflict_info['merged_value'],
                merge_rule=conflict_info['merge_rule'],
                conflict_status='unresolved'
            )

            try:
                conflict_record = crud.conflict_record_crud.create_conflict_record(db, conflict_data)
                created_records.append(conflict_record)
            except Exception as e:
                logger.error(f"创建冲突记录失败: {e}")

        return created_records
    
    @staticmethod
    def update_conflict_records_for_guangyun(db: Session, guangyun_record: models.YunshuGuangyun) -> int:
        """
        更新广韵记录的冲突记录
        删除旧的冲突记录，创建新的冲突记录
        返回新的冲突数量
        """
        try:
            # 删除旧的冲突记录
            ConflictDetectionService.delete_conflict_records_for_guangyun(db, guangyun_record.id)
            
            # 检测新的冲突
            conflict_count, conflict_details = ConflictDetectionService.detect_conflicts_for_guangyun(db, guangyun_record)
            
            # 创建新的冲突记录
            if conflict_count > 0:
                ConflictDetectionService.create_conflict_records(db, guangyun_record, conflict_details)
            
            return conflict_count
            
        except Exception as e:
            logger.error(f"更新冲突记录失败: {e}")
            return 0
    
    @staticmethod
    def delete_conflict_records_for_guangyun(db: Session, guangyun_id: int) -> bool:
        """删除指定广韵记录的所有冲突记录"""
        try:
            deleted_count = db.query(models.YunshuConflictRecord).filter(
                models.YunshuConflictRecord.guangyun_id == guangyun_id
            ).delete()
            db.commit()
            return deleted_count > 0
        except Exception as e:
            logger.error(f"删除冲突记录失败: {e}")
            db.rollback()
            return False
