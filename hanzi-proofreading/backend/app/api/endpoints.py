from fastapi import APIRouter, Depends, HTTPException, Query, Body
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from .. import schemas, crud
from ..database import get_db
from ..services.log_service import GuangyunLogService
import json

router = APIRouter()

@router.get("/hanzi/search", response_model=schemas.HanziSearchResult)
async def search_hanzi(
    q: str = Query(..., description="搜索关键词"),
    limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    db: Session = Depends(get_db)
):
    """搜索汉字"""
    try:
        hanzi_list = crud.hanzi_crud.search(db, query=q, limit=limit, offset=offset)
        total = len(hanzi_list)  # 简化版本，实际应该查询总数
        
        return schemas.HanziSearchResult(
            hanzi_list=hanzi_list,
            total=total
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@router.get("/hanzi/{unicode_code}", response_model=schemas.HanziDetail)
async def get_hanzi_detail(
    unicode_code: str,
    db: Session = Depends(get_db)
):
    """获取汉字详情"""
    hanzi = crud.hanzi_crud.get_detail(db, unicode_code)
    if not hanzi:
        raise HTTPException(status_code=404, detail="汉字不存在")
    
    return hanzi

@router.get("/hanzi/{unicode_code}/relation-group", response_model=schemas.HanziRelationGroup)
async def get_hanzi_relation_group(
    unicode_code: str,
    db: Session = Depends(get_db)
):
    """获取汉字关系网络（核心API）"""
    try:
        # 首先验证汉字是否存在
        hanzi = crud.hanzi_crud.get_by_unicode(db, unicode_code)
        if not hanzi:
            raise HTTPException(status_code=404, detail="汉字不存在")
        
        # 获取关系网络
        relation_group = crud.network_crud.get_relation_group(db, unicode_code)
        return relation_group
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取关系网络失败: {str(e)}")

@router.put("/hanzi/{unicode_code}/relations", response_model=schemas.ApiResponse)
async def update_hanzi_relations(
    unicode_code: str,
    batch_update: schemas.BatchRelationUpdate,
    db: Session = Depends(get_db)
):
    """更新汉字关系"""
    try:
        # 验证汉字是否存在
        hanzi = crud.hanzi_crud.get_by_unicode(db, unicode_code)
        if not hanzi:
            raise HTTPException(status_code=404, detail="汉字不存在")
        
        # 更新关系
        success = crud.network_crud.update_hanzi_relations(db, unicode_code, batch_update)
        
        if success:
            return schemas.ApiResponse(
                success=True,
                message="关系更新成功",
                data={"unicode_code": unicode_code}
            )
        else:
            raise HTTPException(status_code=500, detail="关系更新失败")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新关系失败: {str(e)}")

@router.post("/hanzi", response_model=schemas.Hanzi)
async def create_hanzi(
    hanzi: schemas.HanziCreate,
    db: Session = Depends(get_db)
):
    """创建汉字"""
    try:
        # 检查是否已存在
        existing = crud.hanzi_crud.get_by_unicode(db, hanzi.unicode_code)
        if existing:
            raise HTTPException(status_code=400, detail="汉字已存在")
        
        new_hanzi = crud.hanzi_crud.create(db, hanzi)
        return new_hanzi
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建汉字失败: {str(e)}")

@router.delete("/hanzi/relation/{relation_id}", response_model=schemas.ApiResponse)
async def delete_hanzi_relation(
    relation_id: int,
    db: Session = Depends(get_db)
):
    """删除汉字关系"""
    try:
        success = crud.relation_crud.delete(db, relation_id)
        if success:
            return schemas.ApiResponse(
                success=True,
                message="关系删除成功",
                data={"relation_id": relation_id}
            )
        else:
            raise HTTPException(status_code=404, detail="关系不存在")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除关系失败: {str(e)}")

@router.get("/hanzi/{unicode_code}/metadata", response_model=Optional[schemas.HanziMetadata])
async def get_hanzi_metadata(
    unicode_code: str,
    db: Session = Depends(get_db)
):
    """获取汉字元数据"""
    try:
        # 验证汉字是否存在
        hanzi = crud.hanzi_crud.get_by_unicode(db, unicode_code)
        if not hanzi:
            raise HTTPException(status_code=404, detail="汉字不存在")

        metadata = crud.metadata_crud.get_by_hanzi(db, unicode_code)
        return metadata

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取元数据失败: {str(e)}")

@router.get("/hanzi/{unicode_code}/zixing", response_model=Optional[schemas.HanziZixing])
async def get_hanzi_zixing(
    unicode_code: str,
    db: Session = Depends(get_db)
):
    """获取汉字字形信息"""
    try:
        # 验证汉字是否存在
        hanzi = crud.hanzi_crud.get_by_unicode(db, unicode_code)
        if not hanzi:
            raise HTTPException(status_code=404, detail="汉字不存在")

        zixing = crud.zixing_crud.get_by_unicode(db, unicode_code)
        return zixing

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取字形信息失败: {str(e)}")

@router.post("/hanzi/{unicode_code}/metadata", response_model=schemas.HanziMetadata)
async def create_or_update_hanzi_metadata(
    unicode_code: str,
    metadata: schemas.HanziMetadataCreate,
    db: Session = Depends(get_db)
):
    """创建或更新汉字元数据"""
    try:
        # 验证汉字是否存在
        hanzi = crud.hanzi_crud.get_by_unicode(db, unicode_code)
        if not hanzi:
            raise HTTPException(status_code=404, detail="汉字不存在")
        
        # 确保元数据的unicode_code与路径参数一致
        metadata.unicode_code = unicode_code
        
        result_metadata = crud.metadata_crud.create_or_update(db, metadata)
        return result_metadata
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建或更新元数据失败: {str(e)}")

@router.patch("/hanzi/{unicode_code}/metadata/{category}", response_model=schemas.ApiResponse)
async def update_hanzi_category(
    unicode_code: str,
    category: str,
    value: bool,
    db: Session = Depends(get_db)
):
    """更新汉字的特定类别"""
    try:
        # 验证汉字是否存在
        hanzi = crud.hanzi_crud.get_by_unicode(db, unicode_code)
        if not hanzi:
            raise HTTPException(status_code=404, detail="汉字不存在")
        
        # 验证category值是否有效
        valid_categories = ["fanTiZi", "jianTiZi", "zhengTiZi", "yiTiZi"]
        if category not in valid_categories:
            raise HTTPException(status_code=400, detail=f"无效的类别：{category}")
        
        success = crud.metadata_crud.update_category(db, unicode_code, category, value)
        if success:
            return schemas.ApiResponse(
                success=True,
                message=f"类别 {category} 更新成功",
                data={"unicode_code": unicode_code, "category": category, "value": value}
            )
        else:
            raise HTTPException(status_code=500, detail="类别更新失败")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新类别失败: {str(e)}")

@router.get("/guangyun/{hanzi}", response_model=Dict[str, Any])
async def get_guangyun_data(
    hanzi: str,
    db: Session = Depends(get_db)
):
    """获取汉字的广韵数据（校对数据 + 原始数据）"""
    try:
        # 参数验证
        if not hanzi:
            raise HTTPException(status_code=400, detail="汉字参数不能为空")

        if len(hanzi) != 1:
            raise HTTPException(status_code=400, detail=f"参数必须是单个汉字，收到: '{hanzi}' (长度: {len(hanzi)})")

        # 将汉字转换为Unicode编码（数据库中存储的格式是不带U+前缀的）
        unicode_code = f"{ord(hanzi):04X}"

        # 基本汉字信息（总是返回）
        basic_info = {
            "hanzi": hanzi,
            "unicode": f"U+{unicode_code}",
        }

        # 1. 从yunshu_guangyun表获取校对数据
        guangyun_records = crud.yunshu_guangyun_crud.get_by_unicode(db, unicode_code)

        # 处理校对数据（支持多音字）
        merged_data = []
        if guangyun_records:
            # 有校对数据，处理所有读音
            for record in guangyun_records:
                pronunciation_data = {
                    "id": record.id,  # 添加ID字段用于更新
                    "source": "merged",
                    "hanzi": record.hanzi,
                    "unicode": f"U+{record.unicode}",
                    "fan_qie": record.fan_qie or "-",
                    "sheng_mu": record.sheng_mu or "-",
                    "yun_bu": record.yun_bu or "-",
                    "sheng_diao": record.sheng_diao or "-",
                    "kai_he": record.kai_he or "-",
                    "deng_di": record.deng_di or "-",
                    "she": record.she or "-",
                    "xiao_yun": record.xiao_yun or "-",
                    "qing_zhuo": record.qing_zhuo or "-",
                    "shi_yi": record.shi_yi or "-",
                    "conflicts": record.conflicts or 0  # 添加冲突数量字段
                }
                merged_data.append(pronunciation_data)
        # 没有校对数据时，merged_data保持为空数组

        # 2. 从yunshu_gy_origin表获取原始数据
        # 获取guangyun记录的ID列表，用于查找关联的原始数据
        guangyun_ids = [record.id for record in guangyun_records] if guangyun_records else []

        # 使用新的取数逻辑：既取当前Unicode的记录，也取ref为guangyun.id的记录
        gy_records = crud.gy_origin_crud.get_by_unicode_and_refs(db, unicode_code, guangyun_ids)

        # 处理原始数据
        sources_data = {}
        if gy_records:
            for record in gy_records:
                source_name = record.source

                source_data = {
                    "id": record.id,  # 添加ID字段用于更新
                    "source": source_name,
                    "hanzi": record.hanzi,
                    "unicode": f"U+{record.unicode}",
                    "fan_qie": record.fan_qie or "-",
                    "sheng_mu": record.sheng_mu or "-",
                    "yun_bu": record.yun_bu or "-",
                    "sheng_diao": record.sheng_diao or "-",
                    "kai_he": record.kai_he or "-",
                    "deng_di": record.deng_di or "-",
                    "she": record.she or "-",
                    "xiao_yun": record.xiao_yun or "-",
                    "qing_zhuo": record.qing_zhuo or "-",
                    "shi_yi": record.shi_yi or "-",
                    "order_num": record.order_num,
                    "ref": record.ref
                }

                # 如果该来源还没有数据，创建数组
                if source_name not in sources_data:
                    sources_data[source_name] = []

                # 添加到该来源的数组中
                sources_data[source_name].append(source_data)

        return {
            **basic_info,
            "merged_data": merged_data,
            "sources": sources_data,
            "source_count": len(sources_data),
            "has_proofreading_data": len(guangyun_records) > 0
        }

    except HTTPException:
        raise
    except Exception as e:
        import traceback
        error_detail = f"获取广韵数据失败: {str(e)}\n{traceback.format_exc()}"
        print(f"API Error: {error_detail}")  # 添加控制台日志
        raise HTTPException(status_code=500, detail=f"获取广韵数据失败: {str(e)}")

@router.get("/guangyun/unicode/{unicode_code}", response_model=Dict[str, Any])
async def get_guangyun_data_by_unicode(
    unicode_code: str,
    db: Session = Depends(get_db)
):
    """根据Unicode编码获取汉字的广韵数据（校对数据 + 原始数据）"""
    try:
        # 标准化Unicode格式（移除U+前缀，因为数据库中不存储前缀）
        if unicode_code.startswith('U+'):
            unicode_code = unicode_code[2:]

        # 尝试获取汉字字符
        hanzi = None
        try:
            hanzi = chr(int(unicode_code, 16))
        except ValueError:
            hanzi = "?"

        # 基本汉字信息（总是返回）
        basic_info = {
            "hanzi": hanzi,
            "unicode": f"U+{unicode_code}",
        }

        # 1. 从yunshu_guangyun表获取校对数据
        guangyun_records = crud.yunshu_guangyun_crud.get_by_unicode(db, unicode_code)

        # 处理校对数据（支持多音字）
        merged_data = []
        if guangyun_records:
            # 有校对数据，处理所有读音
            for record in guangyun_records:
                pronunciation_data = {
                    "id": record.id,  # 添加ID字段用于更新
                    "source": "merged",
                    "hanzi": record.hanzi,
                    "unicode": f"U+{record.unicode}",
                    "fan_qie": record.fan_qie or "-",
                    "sheng_mu": record.sheng_mu or "-",
                    "yun_bu": record.yun_bu or "-",
                    "sheng_diao": record.sheng_diao or "-",
                    "kai_he": record.kai_he or "-",
                    "deng_di": record.deng_di or "-",
                    "she": record.she or "-",
                    "xiao_yun": record.xiao_yun or "-",
                    "qing_zhuo": record.qing_zhuo or "-",
                    "shi_yi": record.shi_yi or "-",
                    "conflicts": record.conflicts or 0  # 添加冲突数量字段
                }
                merged_data.append(pronunciation_data)
        # 没有校对数据时，merged_data保持为空数组

        # 2. 从yunshu_gy_origin表获取原始数据
        # 获取guangyun记录的ID列表，用于查找关联的原始数据
        guangyun_ids = [record.id for record in guangyun_records] if guangyun_records else []

        # 使用新的取数逻辑：既取当前Unicode的记录，也取ref为guangyun.id的记录
        gy_records = crud.gy_origin_crud.get_by_unicode_and_refs(db, unicode_code, guangyun_ids)

        # 处理原始数据
        sources_data = {}
        if gy_records:
            for record in gy_records:
                source_name = record.source

                source_data = {
                    "id": record.id,  # 添加ID字段用于更新
                    "source": source_name,
                    "hanzi": record.hanzi,
                    "unicode": f"U+{record.unicode}",
                    "fan_qie": record.fan_qie or "-",
                    "sheng_mu": record.sheng_mu or "-",
                    "yun_bu": record.yun_bu or "-",
                    "sheng_diao": record.sheng_diao or "-",
                    "kai_he": record.kai_he or "-",
                    "deng_di": record.deng_di or "-",
                    "she": record.she or "-",
                    "xiao_yun": record.xiao_yun or "-",
                    "qing_zhuo": record.qing_zhuo or "-",
                    "shi_yi": record.shi_yi or "-",
                    "order_num": record.order_num,
                    "ref": record.ref
                }

                # 如果该来源还没有数据，创建数组
                if source_name not in sources_data:
                    sources_data[source_name] = []

                # 添加到该来源的数组中
                sources_data[source_name].append(source_data)

        return {
            **basic_info,
            "merged_data": merged_data,
            "sources": sources_data,
            "source_count": len(sources_data),
            "has_proofreading_data": len(guangyun_records) > 0
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取广韵数据失败: {str(e)}")

@router.put("/guangyun/{record_id}", response_model=schemas.ApiResponse)
async def update_guangyun_record(
    record_id: int,
    guangyun_update: schemas.YunshuGuangyunUpdate,
    db: Session = Depends(get_db)
):
    """更新单个广韵数据记录"""
    try:
        # 更新记录
        updated_record = crud.yunshu_guangyun_crud.update(db, record_id, guangyun_update)

        if not updated_record:
            raise HTTPException(status_code=404, detail="记录不存在")

        # 更新冲突记录
        try:
            from app.services.conflict_service import ConflictDetectionService
            print(f"开始更新冲突记录，广韵记录ID: {updated_record.id}")
            actual_conflict_count = ConflictDetectionService.update_conflict_records_for_guangyun(db, updated_record)
            print(f"检测到的冲突数量: {actual_conflict_count}")

            # 如果实际冲突数量与传入的不同，更新记录
            if actual_conflict_count != updated_record.conflicts:
                print(f"更新冲突数量从 {updated_record.conflicts} 到 {actual_conflict_count}")
                guangyun_update_conflicts = schemas.YunshuGuangyunUpdate(conflicts=actual_conflict_count)
                updated_record = crud.yunshu_guangyun_crud.update(db, record_id, guangyun_update_conflicts)
        except Exception as conflict_error:
            # 冲突记录更新失败不影响主要操作，但记录日志
            import traceback
            error_detail = f"更新冲突记录失败: {str(conflict_error)}\n{traceback.format_exc()}"
            print(error_detail)

        return schemas.ApiResponse(
            success=True,
            message="广韵数据更新成功",
            data={
                "id": updated_record.id,
                "unicode": updated_record.unicode,
                "hanzi": updated_record.hanzi,
                "fan_qie": updated_record.fan_qie,
                "sheng_mu": updated_record.sheng_mu,
                "yun_bu": updated_record.yun_bu,
                "sheng_diao": updated_record.sheng_diao,
                "kai_he": updated_record.kai_he,
                "deng_di": updated_record.deng_di,
                "she": updated_record.she,
                "xiao_yun": updated_record.xiao_yun,
                "qing_zhuo": updated_record.qing_zhuo,
                "shi_yi": updated_record.shi_yi,
                "conflicts": updated_record.conflicts
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        import traceback
        error_detail = f"更新广韵数据失败: {str(e)}\n{traceback.format_exc()}"
        print(f"API Error: {error_detail}")
        raise HTTPException(status_code=500, detail=f"更新广韵数据失败: {str(e)}")

@router.post("/guangyun", response_model=schemas.ApiResponse)
async def create_guangyun_record(
    guangyun_create: schemas.YunshuGuangyunCreate,
    db: Session = Depends(get_db)
):
    """创建新的广韵数据记录"""
    try:
        # 创建新记录
        new_record = crud.yunshu_guangyun_crud.create(db, guangyun_create)

        # 创建冲突记录
        try:
            from app.services.conflict_service import ConflictDetectionService
            actual_conflict_count = ConflictDetectionService.update_conflict_records_for_guangyun(db, new_record)

            # 如果实际冲突数量与传入的不同，更新记录
            if actual_conflict_count != new_record.conflicts:
                guangyun_update_conflicts = schemas.YunshuGuangyunUpdate(conflicts=actual_conflict_count)
                new_record = crud.yunshu_guangyun_crud.update(db, new_record.id, guangyun_update_conflicts)
        except Exception as conflict_error:
            # 冲突记录创建失败不影响主要操作，但记录日志
            print(f"创建冲突记录失败: {str(conflict_error)}")

        # 记录创建日志
        try:
            GuangyunLogService.log_proofreading_create(
                db,
                new_record.unicode,
                new_record.hanzi,
                new_record.id,
                guangyun_create.model_dump()
            )
        except Exception as log_error:
            # 日志记录失败不影响主要操作
            print(f"记录创建日志失败: {str(log_error)}")

        return schemas.ApiResponse(
            success=True,
            message="广韵数据创建成功",
            data={
                "id": new_record.id,
                "unicode": new_record.unicode,
                "hanzi": new_record.hanzi,
                "conflicts": new_record.conflicts
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        error_detail = f"创建广韵数据失败: {str(e)}\n{traceback.format_exc()}"
        print(f"API Error: {error_detail}")
        raise HTTPException(status_code=500, detail=f"创建广韵数据失败: {str(e)}")

@router.put("/guangyun/origin/{record_id}", response_model=schemas.ApiResponse)
async def update_guangyun_origin_record(
    record_id: int,
    gy_origin_update: schemas.YinyunGyOriginUpdate,
    db: Session = Depends(get_db)
):
    """更新单个广韵原始数据记录"""
    try:
        # 更新记录
        updated_record = crud.gy_origin_crud.update(db, record_id, gy_origin_update)

        if not updated_record:
            raise HTTPException(status_code=404, detail="记录不存在")

        return schemas.ApiResponse(
            success=True,
            message="广韵原始数据更新成功",
            data={
                "id": updated_record.id,
                "unicode": updated_record.unicode,
                "source": updated_record.source,
                "hanzi": updated_record.hanzi,
                "order_num": updated_record.order_num,
                "ref": updated_record.ref,
                "fan_qie": updated_record.fan_qie,
                "sheng_mu": updated_record.sheng_mu,
                "yun_bu": updated_record.yun_bu,
                "sheng_diao": updated_record.sheng_diao,
                "kai_he": updated_record.kai_he,
                "deng_di": updated_record.deng_di,
                "she": updated_record.she,
                "xiao_yun": updated_record.xiao_yun,
                "qing_zhuo": updated_record.qing_zhuo,
                "shi_yi": updated_record.shi_yi
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        import traceback
        error_detail = f"更新广韵原始数据失败: {str(e)}\n{traceback.format_exc()}"
        print(f"API Error: {error_detail}")
        raise HTTPException(status_code=500, detail=f"更新广韵原始数据失败: {str(e)}")

@router.get("/guangyun/{hanzi}/logs")
async def get_guangyun_logs(
    hanzi: str,
    limit: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """获取汉字的校对记录日志"""
    try:
        # 参数验证
        if not hanzi:
            raise HTTPException(status_code=400, detail="汉字参数不能为空")

        if len(hanzi) != 1:
            raise HTTPException(status_code=400, detail=f"参数必须是单个汉字，收到: '{hanzi}' (长度: {len(hanzi)})")

        from app.services.log_service import GuangyunLogService

        logs = GuangyunLogService.get_logs_by_hanzi(db, hanzi, limit)

        return {
            "success": True,
            "message": f"成功获取汉字 '{hanzi}' 的校对记录",
            "data": [
                {
                    "id": log.id,
                    "unicode": log.unicode,
                    "hanzi": log.hanzi,
                    "book_id": log.book_id,
                    "ref_id": log.ref_id,
                    "content": log.content,
                    "create_at": log.create_at.isoformat(),
                    "update_at": log.update_at.isoformat() if log.update_at else None
                }
                for log in logs
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取校对记录失败: {str(e)}")

@router.get("/guangyun/record/{record_id}/logs")
async def get_record_logs(
    record_id: int,
    limit: int = Query(20, ge=1, le=50),
    db: Session = Depends(get_db)
):
    """获取特定记录的校对日志"""
    try:
        from app.services.log_service import GuangyunLogService

        logs = GuangyunLogService.get_logs_by_record_id(db, record_id, limit)

        return {
            "success": True,
            "message": f"成功获取记录 {record_id} 的校对日志",
            "data": [
                {
                    "id": log.id,
                    "unicode": log.unicode,
                    "hanzi": log.hanzi,
                    "book_id": log.book_id,
                    "ref_id": log.ref_id,
                    "content": log.content,
                    "create_at": log.create_at.isoformat(),
                    "update_at": log.update_at.isoformat() if log.update_at else None
                }
                for log in logs
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取记录日志失败: {str(e)}")


@router.get("/dashboard/overview", response_model=Dict[str, Any])
async def get_dashboard_overview(db: Session = Depends(get_db)):
    """获取仪表板概览数据"""
    try:
        overview_data = crud.dashboard_crud.get_overview_stats(db)
        return {
            "success": True,
            "data": overview_data
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取概览数据失败: {str(e)}")



@router.get("/dashboard/activities", response_model=Dict[str, Any])
async def get_dashboard_activities(
    limit: int = Query(10, ge=1, le=50, description="返回数量限制"),
    db: Session = Depends(get_db)
):
    """获取最近活动数据"""
    try:
        activities = crud.dashboard_crud.get_recent_activities(db, limit)
        return {
            "success": True,
            "data": activities
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取活动数据失败: {str(e)}")


@router.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "ok", "message": "汉字校对系统API正常运行"}


# 冲突记录相关API
@router.get("/conflicts/statistics", response_model=schemas.ConflictStatistics)
async def get_conflict_statistics(db: Session = Depends(get_db)):
    """获取冲突统计信息"""
    try:
        statistics = crud.conflict_record_crud.get_conflict_statistics(db)
        return statistics
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取冲突统计失败: {str(e)}")


@router.get("/conflicts/by-field", response_model=List[schemas.ConflictByField])
async def get_conflicts_by_field(db: Session = Depends(get_db)):
    """按字段统计冲突"""
    try:
        conflicts_by_field = crud.conflict_record_crud.get_conflicts_by_field(db)
        return conflicts_by_field
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取字段冲突统计失败: {str(e)}")


@router.get("/conflicts/list", response_model=List[schemas.YunshuConflictRecord])
async def get_conflicts_list(
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    status: Optional[str] = Query(None, description="冲突状态过滤"),
    unicode: Optional[str] = Query(None, description="Unicode编码过滤"),
    fan_qie: Optional[str] = Query(None, description="反切值过滤"),
    db: Session = Depends(get_db)
):
    """获取冲突列表"""
    try:
        conflicts = crud.conflict_record_crud.get_conflicts_list(db, skip, limit, status, unicode, fan_qie)
        return conflicts
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取冲突列表失败: {str(e)}")


@router.put("/conflicts/{conflict_id}/resolve", response_model=schemas.YunshuConflictRecord)
async def resolve_conflict(
    conflict_id: int,
    resolution_method: str = Body(..., description="解决方法"),
    resolution_note: Optional[str] = Body(None, description="解决说明"),
    db: Session = Depends(get_db)
):
    """解决冲突"""
    try:
        update_data = schemas.YunshuConflictRecordUpdate(
            conflict_status="resolved",
            resolution_method=resolution_method,
            resolution_note=resolution_note
        )

        updated_conflict = crud.conflict_record_crud.update_conflict_status(db, conflict_id, update_data)
        if not updated_conflict:
            raise HTTPException(status_code=404, detail="冲突记录不存在")

        return updated_conflict
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"解决冲突失败: {str(e)}")


@router.put("/conflicts/{conflict_id}/ignore", response_model=schemas.YunshuConflictRecord)
async def ignore_conflict(
    conflict_id: int,
    reason: Optional[str] = Body(None, description="忽略原因"),
    db: Session = Depends(get_db)
):
    """忽略冲突"""
    try:
        update_data = schemas.YunshuConflictRecordUpdate(
            conflict_status="ignored",
            resolution_method="ignored",
            resolution_note=reason
        )

        updated_conflict = crud.conflict_record_crud.update_conflict_status(db, conflict_id, update_data)
        if not updated_conflict:
            raise HTTPException(status_code=404, detail="冲突记录不存在")

        return updated_conflict
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"忽略冲突失败: {str(e)}")


@router.put("/conflicts/{conflict_id}", response_model=schemas.YunshuConflictRecord)
async def update_conflict(
    conflict_id: int,
    update_data: schemas.YunshuConflictRecordUpdate,
    db: Session = Depends(get_db)
):
    """更新冲突记录"""
    try:
        updated_conflict = crud.conflict_record_crud.update_conflict(db, conflict_id, update_data)
        if not updated_conflict:
            raise HTTPException(status_code=404, detail="冲突记录不存在")

        return updated_conflict
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新冲突失败: {str(e)}")