#!/usr/bin/env python3
"""
测试冲突检测逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import SessionLocal
from app import models, crud
from app.services.conflict_service import ConflictDetectionService

def test_conflict_detection():
    """测试冲突检测"""
    db = SessionLocal()
    
    try:
        # 查找Unicode为2004A的广韵记录
        guangyun_record = db.query(models.YunshuGuangyun).filter(
            models.YunshuGuangyun.unicode == '2004A'
        ).first()
        
        if not guangyun_record:
            print("未找到Unicode为2004A的广韵记录")
            return
            
        print(f"找到广韵记录: ID={guangyun_record.id}, 汉字={guangyun_record.hanzi}")
        
        # 查看关联的原始数据
        origin_records = crud.gy_origin_crud.get_by_ref(db, guangyun_record.id)
        print(f"关联的原始记录数: {len(origin_records)}")
        
        for record in origin_records:
            print(f"  ID: {record.id}, 来源: {record.source}, 汉字: {record.hanzi}")
            print(f"    小韵: '{record.xiao_yun}', 声母: '{record.sheng_mu}'")
        
        # 测试冲突检测
        print("\n开始冲突检测...")
        conflict_count, conflict_details = ConflictDetectionService.detect_conflicts_for_guangyun(db, guangyun_record)
        
        print(f"检测到 {conflict_count} 个冲突")
        for detail in conflict_details:
            print(f"  冲突字段: {detail['field_display_name']} ({detail['field_name']})")
            print(f"    各源值: {detail['field_values']}")
            print(f"    合并值: {detail['merged_value']}")
            print(f"    合并规则: {detail['merge_rule']}")
        
        # 测试创建冲突记录
        if conflict_count > 0:
            print("\n创建冲突记录...")
            created_records = ConflictDetectionService.create_conflict_records(db, guangyun_record, conflict_details)
            print(f"创建了 {len(created_records)} 条冲突记录")
            
            # 查看创建的冲突记录
            for record in created_records:
                print(f"  冲突记录ID: {record.id}")
                print(f"    字段: {record.field_display_name}")
                print(f"    xxt: {record.xxt_value}, qx: {record.qx_value}, yd: {record.yd_value}")
        
    except Exception as e:
        import traceback
        print(f"测试失败: {e}")
        print(traceback.format_exc())
    finally:
        db.close()

if __name__ == "__main__":
    test_conflict_detection()
