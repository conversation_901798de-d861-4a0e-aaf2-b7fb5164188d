#!/usr/bin/env python3
"""
测试API调用
"""

import requests
import json

def test_api():
    """测试API调用"""
    
    # 测试API调用
    url = 'http://localhost:8000/api/guangyun/70518'
    data = {
        'hanzi': '𠁊',
        'unicode': '2004A',
        'fan_qie': '疏兩切',
        'sheng_mu': '生',
        'yun_bu': '養',
        'sheng_diao': '上',
        'kai_he': '開',
        'deng_di': '三',
        'she': '宕',
        'xiao_yun': '-',
        'qing_zhuo': '全清',
        'shi_yi': '-',
        'conflicts': 0
    }

    try:
        print("发送API请求...")
        response = requests.put(url, json=data, timeout=10)
        print(f'状态码: {response.status_code}')
        print(f'响应: {response.text}')
        
        if response.status_code == 200:
            print("API调用成功")
        else:
            print("API调用失败")
            
    except Exception as e:
        print(f'请求失败: {e}')

if __name__ == "__main__":
    test_api()
